{
  "compilerOptions": {
    "target": "esnext",
    "module": "nodenext",
    "moduleResolution": "nodenext",
    "strict": true,
    "isolatedModules": false,
    "experimentalDecorators": true,
    "esModuleInterop": true,
    "noImplicitThis": true,
    "noImplicitAny": true,
    "strictNullChecks": false,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "jsx": "preserve",
    "outDir": "dist",
    "types": [
      "node"
    ],
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@blueking/*": [
        "./node_modules/@blueking/*"
      ],
    },
    "lib": [
      "esnext",
      "dom",
      "dom.iterable",
      "scripthost"
    ]
  },
  "include": [
    "./node_modules/bkui-vue/lib/volar.components.d.ts",
    "./**/*",
    ".eslintrc.js",
  ]
}

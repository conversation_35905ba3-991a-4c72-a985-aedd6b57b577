/* CSS Reset */
html,
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
tr,
th,
td,
hgroup,
nav,
section,
article,
aside,
footer,
figure,
figcaption,
menu,
button,
applet,
object,
iframe,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
label,
table,
caption,
tbody,
tfoot,
thead,
canvas,
details,
embed,
main,
output,
ruby,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
hgroup,
menu,
nav,
section {
  display: block;
}

body {
  line-height: 1.5;
  font-size: 12px;
  text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
  outline: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
  font-weight: normal;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

caption,
th {
  text-align: left;
}

fieldset,
img {
  border: 0;
}

li {
  list-style: none;
}

ins {
  text-decoration: none;
}

del {
  text-decoration: line-through;
}

input,
button,
textarea,
select,
optgroup,
option {
  font-family: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  outline: 0;
}

/* different browsers have different calculation methods for the box model width of input, select, and textarea */
input,
select,
textarea {
  box-sizing: content-box;
}

button {
  appearance: none;
  border: 0;
  background: none;
}

a {
  -webkit-touch-callout: none;
  text-decoration: none;
}

:focus {
  outline: 0;
  -webkit-tap-highlight-color: transparent;
}

em,
i {
  font-style: normal;
}

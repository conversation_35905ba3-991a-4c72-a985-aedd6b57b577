<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Icon/AI 小鲸</title>
    <defs>
        <linearGradient x1="45.02491%" y1="35.5927467%" x2="99.2803929%" y2="81.0358394%" id="linearGradient-1">
            <stop stop-color="#235DFA" offset="0%"></stop>
            <stop stop-color="#EB8CEC" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" stop-opacity="0.885701426" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.29" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#1562FC" offset="0%"></stop>
            <stop stop-color="#EB8CEC" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="95.0902604%" x2="35.112422%" y2="19.6450217%" id="linearGradient-4">
            <stop stop-color="#DC63FE" offset="0.0150240385%"></stop>
            <stop stop-color="#235DFA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="46.6072083%" y1="95.8855311%" x2="46.6072083%" y2="50%" id="linearGradient-5">
            <stop stop-color="#B962FD" offset="0%"></stop>
            <stop stop-color="#565FFB" offset="99.984976%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#235DFA" offset="0%"></stop>
            <stop stop-color="#A826E2" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="AI--小鲸" transform="translate(-1561, -1181)">
            <g id="触发入口备份" transform="translate(1553, 1174)">
                <g id="Icon/AI-小鲸" transform="translate(8, 7)">
                    <g id="编组" transform="translate(1.3333, 1.677)">
                        <path d="M2.66705252,15.6739704 L2.66666667,14.3229852 C2.66666667,9.9047072 6.24838867,6.3229852 10.6666667,6.3229852 L14.6666667,6.3229852 C19.0849447,6.3229852 22.6666667,9.9047072 22.6666667,14.3229852 L22.6671955,23.7678689 C25.0214528,24.0035841 26.6666667,24.6713549 26.6666667,26.3034961 C26.6605025,26.3124265 26.656393,26.3137104 26.6495438,26.3148556 L26.6370784,26.3164707 C26.6173529,26.318489 26.5844771,26.3199738 26.5296841,26.3210118 L26.4843429,26.3217078 C26.4760555,26.3218103 26.4673913,26.3219061 26.4583333,26.3219955 L25.6509582,26.3220036 C25.6249144,26.321929 25.5982028,26.3218506 25.5708063,26.3217686 L25.3980713,26.3212348 C25.3678666,26.3211391 25.3369428,26.3210401 25.3052827,26.320938 L25.1063498,26.3202896 C25.0716761,26.3201758 25.0362319,26.3200592 25,26.31994 L24.7730199,26.3191953 C24.654667,26.3188088 24.528917,26.318402 24.3953077,26.3179795 L24.1174728,26.3171149 C23.70457,26.3158517 23.2247877,26.3144847 22.6672933,26.3131209 L22.6666667,26.3229852 L10.6666667,26.3229852 C6.63303794,26.3229852 3.29664679,23.3377563 2.74629514,19.4561004 C1.15124173,18.9334343 0,17.4384093 0,15.6758076 L1.73233032,15.6750674 C1.83856201,15.6749729 1.94299316,15.6748702 2.0456543,15.6747598 L2.66705252,15.6739704 Z" id="形状结合" fill="url(#linearGradient-1)"></path>
                        <path d="M11.3333333,8.98965186 L14,8.98965186 C17.3137085,8.98965186 20,11.6759434 20,14.9896519 L20,21.6563185 C20,22.760888 19.1045695,23.6563185 18,23.6563185 L11.3333333,23.6563185 C8.01962483,23.6563185 5.33333333,20.970027 5.33333333,17.6563185 L5.33333333,14.9896519 C5.33333333,11.6759434 8.01962483,8.98965186 11.3333333,8.98965186 Z" id="矩形" fill="url(#linearGradient-2)"></path>
                        <rect id="矩形" fill="url(#linearGradient-3)" x="24" y="10.3229852" width="2.66666667" height="12" rx="1"></rect>
                        <path d="M25.3125069,1.33333333 C25.3125069,3.37253856 26.8384476,5.05534124 28.8107554,5.30216765 L29.3125069,5.33333333 C27.2733017,5.33333333 25.590499,6.85927407 25.3436726,8.83158183 L25.3125069,9.33333333 C25.3125069,7.2941281 23.7865662,5.61132543 21.8142584,5.36449901 L21.3125069,5.33333333 C23.3517121,5.33333333 25.0345148,3.8073926 25.2813412,1.83508483 L25.3125069,1.33333333 Z" id="星形" fill="url(#linearGradient-4)" fill-rule="nonzero"></path>
                        <path d="M19.9791736,1.92557081e-12 C19.9791736,1.35947015 20.9964674,2.4813386 22.3113392,2.64588955 L22.6458402,2.66666667 C21.2863701,2.66666667 20.1645016,3.68396049 19.9999507,4.99883233 L19.9791736,5.33333333 C19.9791736,3.97386318 18.9618797,2.85199473 17.6470079,2.68744379 L17.3125069,2.66666667 C18.671977,2.66666667 19.7938455,1.64937284 19.9583964,0.334501001 L19.9791736,1.92557081e-12 Z" id="星形" fill="url(#linearGradient-5)" fill-rule="nonzero"></path>
                        <path d="M21.3125069,6.66666667 C21.3125069,7.34640174 21.8211538,7.90733597 22.4785897,7.98961144 L22.6458402,8 C21.9661051,8 21.4051709,8.50864691 21.3228955,9.16608283 L21.3125069,9.33333333 C21.3125069,8.65359826 20.80386,8.09266403 20.1464241,8.01038856 L19.9791736,8 C20.6589086,8 21.2198429,7.49135309 21.3021183,6.83391717 L21.3125069,6.66666667 Z" id="星形" fill="#8860FC" fill-rule="nonzero"></path>
                        <rect id="矩形" fill="#16204D" x="8.66666667" y="14.3229852" width="2.66666667" height="4" rx="1.33333333"></rect>
                        <rect id="矩形备份" fill="#16204D" x="14" y="14.3229852" width="2.66666667" height="4" rx="1.33333333"></rect>
                        <path d="M16.2528807,6.3229852 C15.8945981,4.54520742 15.1886712,3.65631853 14.1350999,3.65631853 C12.5547431,3.65631853 12.5718314,5.76879538 14.1350999,5.58194526 C14.9597452,5.48337929 15.51887,5.7303926 15.8124743,6.3229852 L16.2528807,6.3229852 Z" id="路径-7" fill="url(#linearGradient-6)" transform="translate(14.6046, 4.9897) scale(-1, 1) translate(-14.6046, -4.9897)"></path>
                        <path d="M12.9562317,6.3229852 C12.5979491,4.54520742 11.8920221,3.65631853 10.8384509,3.65631853 C9.25809406,3.65631853 9.27518239,5.76879538 10.8384509,5.58194526 C11.6630962,5.48337929 12.2222209,5.7303926 12.5158252,6.3229852 L12.9562317,6.3229852 Z" id="路径-7" fill="url(#linearGradient-6)"></path>
                    </g>
                    <rect id="codesign_placeholder_name" x="0" y="0" width="32" height="32"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>
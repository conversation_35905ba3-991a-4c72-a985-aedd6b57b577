:root {
  /* 品牌色 */
  --vp-c-brand: #1482ff;
  --vp-c-brand-light: #4a9eff;
  --vp-c-brand-lighter: #7cb8ff;
  --vp-c-brand-dark: #0a6ad9;
  --vp-c-brand-darker: #0751a5;
  --vp-c-brand-dimm: rgba(20, 130, 255, 0.08);

  /* 辅助色 */
  --vp-c-purple: #8a2be2;
  --vp-c-purple-light: #a25bf0;
  --vp-c-cyan: #00d1b2;
  --vp-c-cyan-light: #29ecd2;

  /* 主题特定样式 */
  --vp-c-bg-alt: #f5f9ff;
  --vp-custom-block-tip-border: var(--vp-c-brand);
  --vp-custom-block-tip-bg: var(--vp-c-brand-dimm);

  /* 字体 */
  --vp-font-family-base: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  --vp-font-family-mono: 'JetBrains Mono', monospace;

  /* 圆角 */
  --vp-border-radius: 8px;
  
  /* 内容宽度 */
  --vp-layout-max-width: 1400px;
}

/* 添加深色模式的变量 */
.dark {
  /* 深色模式主题色调整 */
  --vp-c-brand: #4a9eff; /* 较亮的蓝色 */
  --vp-c-brand-light: #7cb8ff;
  --vp-c-brand-lighter: #a1cbff;
  --vp-c-brand-dark: #1482ff;
  --vp-c-brand-darker: #0a6ad9;
  
  /* 背景色 */
  --vp-c-bg: #1a1a1a;
  --vp-c-bg-alt: #242424;
  --vp-c-bg-soft: #2c2c2c;
  
  /* 文本颜色 */
  --vp-c-text-1: rgba(255, 255, 255, 0.87);
  --vp-c-text-2: rgba(255, 255, 255, 0.6);
  --vp-c-text-3: rgba(255, 255, 255, 0.38);
  
  /* 边框颜色 */
  --vp-c-divider: rgba(255, 255, 255, 0.12);
  --vp-c-divider-light: rgba(255, 255, 255, 0.08);
  
  /* 搜索栏相关 */
  --vp-c-bg-search: #242424;
  --vp-c-bg-search-result: #2c2c2c;
  --vp-c-bg-search-result-hover: #333333;
  
  /* 侧边栏相关 */
  --vp-sidebar-bg-color: var(--vp-c-bg-alt);
  --vp-sidebar-text-color: var(--vp-c-text-1);
  --vp-sidebar-link-color: var(--vp-c-text-1);
  --vp-sidebar-active-color: var(--vp-c-brand);
} 
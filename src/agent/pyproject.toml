[tool.poetry]
name = "aidev-agent"
version = "1.0.0b16"
description = "bkaidev agent builder"
authors = ["blueking"]
license = "MIT"
packages = [{ include = "aidev_agent" }]

[tool.poetry.dependencies]
python = ">=3.10.5,<4.0"
environs = "^14.1.0"
langchain = "^0.3.14"
langchain-openai = "^0.3.0"
langchain-community = "0.3.15"
pyro4 = "^4.82"
langfuse = "<3"
bkapi-client-core = "^1.2.0"
jinja2 = "^3.1.6"
cachetools = "^5.5.2"
asgiref = "^3.8.1"
werkzeug = "^3.1.3"
pytz = "^2025.2"
h11 = "0.16.0"


[tool.poetry.group.dev.dependencies]
ipdb = ">=0.13.13,<0.14.0"
pytest = ">=7.4.4,<7.5.0"
pytest-asyncio = ">=0.23.3,<0.24.0"
pytest-env = ">=1.1.3,<1.2.0"
pytest-cov = ">=4.1.0,<4.2.0"
pytest-timeout = "2.3.1"
pre-commit = "4.0.1"
faker = "^37.0.0"
pytest-mock = "^3.14.0"
pytest-dotenv = "^0.5.2"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.flake8]
extend-ignore = "W503,ANN101,ANN102,ANN002,ANN003,ANN001,ANN201,ANN204,ANN205,E203"
max-line-length = 120
max-complexity = 20
format = "pylint"
show_source = "true"
statistics = "true"
count = "true"

[tool.ruff]
exclude = ["*/config/", "*/migrations/"]
force-exclude = true
line-length = 120
indent-width = 4

[tool.ruff.lint]
select = ["E", "F", "I", "PIE", "PERF", "SIM"]
ignore = ["PERF401", "E501"]

[tool.ruff.format]
quote-style = "double"

[tool.ruff.lint.isort]
# 总是显式制定 import section 的顺序
section-order = [
  "future",
  "standard-library",
  "third-party",
  "first-party",
  "local-folder",
]
relative-imports-order = "closest-to-furthest"

[tool.pytest.ini_options]
env_override_existing_values = 1
env_files = [".env"]
asyncio_default_fixture_loop_scope = "session"
addopts = "-vvv --maxfail=2 -s"
asyncio_mode = "auto"

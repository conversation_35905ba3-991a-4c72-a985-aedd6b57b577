specVersion: 3
modules:
  - name: default
    isDefault: true
    language: python
    spec:
      configuration:
        env:
          - name: PIP_VERSION
            value: 21.0
            description: pip version control
          - name: PYTHONPATH
            value: /app
            description: ADD PYTHONPATH
          - name: BK_APP_CONFIG_PATH
            value: bk_plugin_runtime.config
            description: app config path
          - name: DJ<PERSON>G<PERSON>_SETTINGS_MODULE
            value: bk_plugin.patch.plugin
            description: django settings module
          - name: BK_APIGW_MANAGER_URL_TMPL
            value: {{cookiecutter.apigw_manager_url_tmpl}}
            description: pip version control
          - name: BK_APIGW_MANAGER_MAINTAINERS
            value: {{cookiecutter.init_apigw_maintainer}}
            description: plugin apigw managers
          - name: BK_INIT_SUPERUSER
            value: {{cookiecutter.init_admin}}
            description: plugin admin panel init superuser
          - name: BK_SOPS_APP_CODE
            value: bksops
            description: bk-sops app code
          - name: B<PERSON>_APP_CONFIG_PATH
            value: bk_plugin_runtime.config
            description: blueapps config path
          - name: BK_APIGW_CORS_ALLOW_ORIGINS
            value: "**"
            description: apigw cors strategie allow origins
          - name: BK_APIGW_CORS_ALLOW_METHODS
            value: "**"
            description: apigw cors strategie allow methods
          - name: BK_APIGW_CORS_ALLOW_HEADERS
            value: "**"
            description: apigw cors strategie allow headers
          - name: DJANGO_ALLOW_ASYNC_UNSAFE
            value: "True"
            description: set django allow unsafe async
          {% if cookiecutter.llm_gateway_url -%}
          - name: LLM_GW_ENDPOINT
            value: "{{cookiecutter.llm_gateway_url}}"
            description: LLM gateway endpoint
          {%- endif -%}
          {% if cookiecutter.model_management_ns_host %}
          - name: MODEL_MANAGEMENT_NS_HOST
            value: "{{cookiecutter.model_management_ns_host}}"
            description: model management host
          {%- endif -%}
          {% if cookiecutter.model_management_ns_port %}
          - name: MODEL_MANAGEMENT_NS_PORT
            value: "{{cookiecutter.model_management_ns_port}}"
            description: model management port
          {%- endif -%}
          {% if cookiecutter.similarity_model_service_name %}
          - name: SIMILARITY_MODEL_SERVICE_NAME
            value: "{{cookiecutter.similarity_model_service_name}}"
            description: similarity model service name
          {%- endif -%}
          {% if cookiecutter.llm_service_prefix %}
          - name: LLM_SERVICE_PREFIX
            value: "{{ cookiecutter.llm_service_prefix }}"
            description: LLM service prefix
          {%- endif -%}
          {% if cookiecutter.aidev_gateway_name %}
          - name: AIDEV_GATEWAY_NAME
            value: "{{ cookiecutter.aidev_gateway_name }}"
            description: aidev gateway name
          {%- endif -%}
          {% if cookiecutter.bk_apigw_stage %}
          - name: BK_APIGW_STAGE
            value: "{{ cookiecutter.bk_apigw_stage }}"
            description: bk apigw stage
          {% endif %}
      processes:
        - name: web
          procCommand: gunicorn bk_plugin_runtime.wsgi --timeout 120 -k gthread -w 2 --max-requests=1000 --threads 64
          services:
            - name: web
              protocol: TCP
              exposedType:
                name: bk/http
              targetPort: 5000
              port: 80

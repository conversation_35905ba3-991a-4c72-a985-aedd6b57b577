{"project_name": "my_plugin", "app_code": "the app code of plugin saas", "plugin_desc": "the description of this plugin", "init_admin": "init admin username", "init_apigw_maintainer": "admin", "apigw_manager_url_tmpl": "bk apigw manager url template", "_copy_without_render": [".ci", "*dist", "*tpls"], "agent_description": "aidev agent demo", "chat_model": "hunyuan-turbo", "knowledgebase_ids": "[]", "knowledge_ids": "[]", "tool_codes": "[]", "role_prompt": "[]", "topk": "10", "knowledge_resource_fine_grained_score_type": "LLM", "knowledge_resource_reject_threshold": "(0.001, 0.1)", "independent_query_mode": "SUM_AND_CONCATE", "with_index_specific_search_init": "True", "with_index_specific_search_keywords": "False", "with_index_specific_search_translation": "False", "run_ver": "open", "llm_gateway_url": "", "model_management_ns_host": "", "model_management_ns_port": "", "similarity_model_service_name": "", "llm_service_prefix": "", "aidev_gateway_name": "", "bk_apigw_stage": "", "non_thinking_llm": "", "app_apigw_host": "", "static_template_root": "tpls", "sync_config_from_aidev": "False", "app_setting_page": "", "bk_login_url": "http://login.com", "bk_paas_domain": ".com"}